import { useState } from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { Input } from '@/libs/form/Input';
import { ColorPicker } from '@/libs/ui/ColorPicker/ColorPicker';
import { ProductDisplayCard } from '../ProductDisplayCard/ProductDisplayCard';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import { ProductType } from '@/types';

type AddProductToListModalOptions = ModalOptionProps & {
  product: ProductType;
  selectedOfferId?: string;
};

type ListMode = 'existing' | 'new';

export const AddProductToListModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const { product, selectedOfferId } =
    modalOption as AddProductToListModalOptions;

  const [listMode, setListMode] = useState<ListMode>('new');
  const [listName, setListName] = useState('My Shopping List 4');
  const [selectedColor, setSelectedColor] = useState('#EF4444');
  const [label, setLabel] = useState('Shop Urgent!!');
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateList = async () => {
    setIsLoading(true);

    try {
      // TODO: Implement actual list creation logic
      console.log('Creating list:', {
        mode: listMode,
        listName,
        color: selectedColor,
        label,
        product,
        selectedOfferId,
      });

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      closeModal();
    } catch (error) {
      console.error('Failed to create list:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!product) {
    return null;
  }

  return (
    <Modal name={MODAL_NAME.ADD_PRODUCT_TO_LIST} withCloseButton={true}>
      <div className="max-w-md">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Add Product to List
          </h2>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Description */}
          <p className="text-center text-gray-600">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          </p>

          {/* Mode Toggle */}
          <div className="flex gap-2">
            <Button
              variant={listMode === 'existing' ? 'secondary' : 'white'}
              onClick={() => setListMode('existing')}
              className="flex-1"
              fullWidth={false}
            >
              Add to Existing List
            </Button>
            <Button
              variant={listMode === 'new' ? 'secondary' : 'white'}
              onClick={() => setListMode('new')}
              className="flex-1"
              fullWidth={false}
            >
              Create a New List
            </Button>
          </div>

          {/* List Name Input */}
          <div>
            <Input
              label="List name"
              value={listName}
              onChange={(e) => setListName(e.target.value)}
              placeholder="Enter list name"
            />
          </div>

          {/* Product Display */}
          <ProductDisplayCard
            product={product}
            selectedOfferId={selectedOfferId}
          />

          {/* Color and Label Row */}
          <div className="grid grid-cols-2 gap-4">
            <ColorPicker
              label="Add color"
              value={selectedColor}
              onChange={setSelectedColor}
            />

            <div>
              <Input
                label="Add label"
                value={label}
                onChange={(e) => setLabel(e.target.value)}
                placeholder="Enter label"
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-6 border-t border-gray-200 pt-6">
          <Button
            onClick={handleCreateList}
            loading={isLoading}
            disabled={!listName.trim()}
            className="w-full"
          >
            Create List
          </Button>
        </div>
      </div>
    </Modal>
  );
};
