import { useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { Button } from '@/libs/ui/Button/Button';
import { Input } from '@/libs/form/Input';
import { ColorPicker } from '@/libs/ui/ColorPicker/ColorPicker';
import { ProductDisplayCard } from '../ProductDisplayCard/ProductDisplayCard';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import { ProductType } from '@/types';

type AddProductToListModalOptions = ModalOptionProps & {
  product: ProductType;
  selectedOfferId?: string;
};

type ListMode = 'existing' | 'new';

export const AddProductToListModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const { product, selectedOfferId } =
    modalOption as AddProductToListModalOptions;

  const [listMode, setListMode] = useState<ListMode>('new');
  const [listName, setListName] = useState('My Shopping List 4');
  const [selectedColor, setSelectedColor] = useState('#EF4444');
  const [label, setLabel] = useState('Shop Urgent!!');
  const [isLoading, setIsLoading] = useState(false);

  const isOpen =
    Boolean(modalOption.name) &&
    modalOption.name.includes(MODAL_NAME.ADD_PRODUCT_TO_LIST);

  const handleCreateList = async () => {
    setIsLoading(true);

    try {
      // TODO: Implement actual list creation logic
      console.log('Creating list:', {
        mode: listMode,
        listName,
        color: selectedColor,
        label,
        product,
        selectedOfferId,
      });

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      closeModal();
    } catch (error) {
      console.error('Failed to create list:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!product) {
    return null;
  }

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && closeModal()}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 z-40 bg-black/50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 z-50 max-h-[90vh] w-full max-w-md -translate-x-1/2 -translate-y-1/2 overflow-y-auto rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 p-6">
            <Dialog.Title className="text-xl font-semibold text-gray-900">
              Add Product to List
            </Dialog.Title>
            <Dialog.Close asChild>
              <button className="text-gray-400 transition-colors hover:text-gray-600">
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </Dialog.Close>
          </div>

          {/* Content */}
          <div className="space-y-6 p-6">
            {/* Description */}
            <p className="text-center text-gray-600">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            </p>

            {/* Mode Toggle */}
            <div className="flex gap-2">
              <Button
                variant={listMode === 'existing' ? 'secondary' : 'white'}
                onClick={() => setListMode('existing')}
                className="flex-1"
                fullWidth={false}
              >
                Add to Existing List
              </Button>
              <Button
                variant={listMode === 'new' ? 'secondary' : 'white'}
                onClick={() => setListMode('new')}
                className="flex-1"
                fullWidth={false}
              >
                Create a New List
              </Button>
            </div>

            {/* List Name Input */}
            <div>
              <Input
                label="List name"
                value={listName}
                onChange={(e) => setListName(e.target.value)}
                placeholder="Enter list name"
              />
            </div>

            {/* Product Display */}
            <ProductDisplayCard
              product={product}
              selectedOfferId={selectedOfferId}
            />

            {/* Color and Label Row */}
            <div className="grid grid-cols-2 gap-4">
              <ColorPicker
                label="Add color"
                value={selectedColor}
                onChange={setSelectedColor}
              />

              <div>
                <Input
                  label="Add label"
                  value={label}
                  onChange={(e) => setLabel(e.target.value)}
                  placeholder="Enter label"
                />
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-6">
            <Button
              onClick={handleCreateList}
              loading={isLoading}
              disabled={!listName.trim()}
              className="w-full"
            >
              Create List
            </Button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
